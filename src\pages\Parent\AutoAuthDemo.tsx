import { Button, Card, Space, Typography } from 'antd';
import React from 'react';

const { Title, Paragraph, Text } = Typography;

/**
 * 家长端自动认证功能演示页面
 * @description 仅在开发环境下使用，用于测试自动认证功能
 */
const AutoAuthDemo: React.FC = () => {
  // 仅在开发模式下渲染
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const testCases = [
    {
      title: '正常手机号测试',
      description: '使用有效的手机号格式进行自动认证',
      url: '/parent?phone=13186034256',
      type: 'primary',
    },
    {
      title: '无效格式测试',
      description: '使用无效的手机号格式，应显示错误提示',
      url: '/parent?phone=1234567890',
      type: 'default',
    },
    {
      title: '包含字母测试',
      description: '手机号包含字母，应显示格式错误',
      url: '/parent?phone=abc1234567',
      type: 'default',
    },
    {
      title: '空参数测试',
      description: '不包含phone参数，正常显示手动验证页面',
      url: '/parent',
      type: 'default',
    },
    {
      title: '多参数测试',
      description: '包含多个URL参数的情况',
      url: '/parent?phone=13186034256&test=1&debug=true',
      type: 'default',
    },
  ];

  const handleTestClick = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title level={2}>家长端自动认证功能测试</Title>
        <Paragraph>
          <Text type="secondary">
            此页面仅在开发环境下可见，用于测试家长端的自动认证功能。
            点击下方按钮可以在新窗口中测试不同的场景。
          </Text>
        </Paragraph>

        <Title level={3}>测试用例</Title>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {testCases.map((testCase, index) => (
            <Card
              key={index}
              size="small"
              title={testCase.title}
              extra={
                <Button
                  type={testCase.type as any}
                  onClick={() => handleTestClick(testCase.url)}
                >
                  测试
                </Button>
              }
            >
              <Paragraph>
                <Text>{testCase.description}</Text>
              </Paragraph>
              <Paragraph>
                <Text code>{window.location.origin + testCase.url}</Text>
              </Paragraph>
            </Card>
          ))}
        </Space>

        <Title level={3}>功能说明</Title>
        <Paragraph>
          <ul>
            <li>
              <Text strong>自动认证：</Text>
              当URL中包含<Text code>phone</Text>参数时，系统会自动尝试验证该手机号
            </li>
            <li>
              <Text strong>格式验证：</Text>
              系统会先验证手机号格式（11位数字，1开头），格式错误时显示提示
            </li>
            <li>
              <Text strong>预填充：</Text>
              即使自动验证失败，手机号输入框也会预填充URL中的手机号
            </li>
            <li>
              <Text strong>错误处理：</Text>
              验证失败时显示错误信息，用户可以手动重新输入
            </li>
            <li>
              <Text strong>参数清理：</Text>
              验证成功后会自动移除URL中的phone参数，避免重复验证
            </li>
          </ul>
        </Paragraph>

        <Title level={3}>注意事项</Title>
        <Paragraph>
          <ul>
            <li>自动认证功能与现有的手动验证流程完全兼容</li>
            <li>URL中的手机号参数在地址栏中可见，请注意隐私保护</li>
            <li>建议在生产环境中考虑使用加密参数或其他安全措施</li>
            <li>此演示页面仅在开发环境下可见</li>
          </ul>
        </Paragraph>
      </Card>
    </div>
  );
};

export default AutoAuthDemo;
