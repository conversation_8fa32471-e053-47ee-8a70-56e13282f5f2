import {
  BugOutlined,
  ExperimentOutlined,
  UserOutlined,
  MobileOutlined,
} from '@ant-design/icons';
import { history } from '@umijs/max';
import { Tooltip } from 'antd';
import React from 'react';
import './index.less';

/**
 * 开发工具组件
 * @description 仅在开发模式下显示的工具链接
 */
const DevTools: React.FC = () => {
  // 仅在开发模式下渲染
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const devLinks = [
    {
      key: 'star-rating',
      icon: <ExperimentOutlined />,
      title: '星级评分演示',
      path: '/star-rating-demo',
      description: '查看星级评分组件的完整演示',
    },
    {
      key: 'parent',
      icon: <UserOutlined />,
      title: '家长端演示',
      path: '/parent',
      description: '查看家长端问卷填写流程',
    },
    {
      key: 'parent-auto-auth',
      icon: <MobileOutlined />,
      title: '自动认证演示',
      path: '/parent-auto-auth-demo',
      description: '测试家长端自动认证功能',
    },
  ];

  const handleLinkClick = (path: string) => {
    history.push(path);
  };

  return (
    <div className="dev-tools">
      <div className="dev-tools-header">
        <BugOutlined className="dev-icon" />
        <span className="dev-title">开发工具</span>
      </div>

      <div className="dev-tools-links">
        {devLinks.map((link) => (
          <Tooltip key={link.key} title={link.description} placement="right">
            <div
              className="dev-link"
              onClick={() => handleLinkClick(link.path)}
            >
              <span className="dev-link-icon">{link.icon}</span>
              <span className="dev-link-text">{link.title}</span>
            </div>
          </Tooltip>
        ))}
      </div>

      <div className="dev-tools-footer">
        <span className="dev-env-badge">DEV</span>
      </div>
    </div>
  );
};

export default DevTools;
