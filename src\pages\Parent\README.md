# 家长端问卷填写流程

家长端问卷填写系统，提供四级页面流程，支持手机号验证、学生选择、问卷选择和问卷评价功能。

## 功能特性

### 📱 手机号验证页 (`PhoneVerify`)

- **身份验证**：通过家长手机号验证身份
- **关联检查**：验证手机号是否关联学生信息
- **自动认证**：支持URL参数自动验证手机号
- **错误提示**：显示详细的错误信息和解决建议
- **响应式设计**：适配移动端和桌面端

**主要功能：**

- 手机号格式验证（11 位数字，1 开头）
- 调用后端 SSO 验证接口
- URL参数自动认证（`?phone=手机号`）
- 手机号预填充功能
- 错误状态提示（手机号无效、未关联学生等）
- 验证成功后自动跳转学生选择页

### 👨‍🎓 学生选择页 (`StudentSelect`)

- **学生列表**：展示该手机号关联的所有学生
- **学生信息**：显示姓名、班级、年级等详细信息
- **多学生支持**：支持一个家长关联多个学生的场景
- **状态标识**：显示学生在读状态

**主要功能：**

- 学生卡片式展示（头像、姓名、班级、年级）
- 年级标签颜色区分
- 点击学生卡片跳转问卷选择页
- 返回上一步功能

### 📋 问卷选择页 (`QuestionnaireSelect`)

- **问卷列表**：展示该学生所有可填写的问卷
- **班级分组**：按班级分组显示不同的问卷
- **状态显示**：显示问卷状态（待填写/已提交/已截止）
- **时间控制**：显示问卷开始和截止时间

**主要功能：**

- 问卷卡片式展示（标题、月份、状态、时间）
- 按班级分组展示问卷
- 状态标签区分（待填写、已提交、已截止）
- 只允许填写有效期内的问卷
- 点击问卷卡片跳转问卷填写页

### 📝 问卷填写页 (`EvaluationForm`)

- **学校评价**：星级评分 + 文本描述
- **教师评价**：每个教师独立评分和评价
- **进度跟踪**：实时显示评价完成进度
- **本地缓存**：自动保存填写内容到 localStorage
- **状态管理**：已填写教师显示完成状态

**主要功能：**

- 顶部学校评分区域（支持 5 星制/10 星制）
- 中部教师列表（姓名、学科、部门信息）
- 底部提交按钮（所有教师评分完成后才激活）
- 评分进度条显示
- 本地数据缓存和恢复

## 技术实现

### 🏗️ 架构设计

```
src/pages/Parent/
├── index.tsx              # 主页面路由容器
├── PhoneVerify/           # 手机号验证页
│   ├── index.tsx
│   └── index.less
├── StudentSelect/         # 学生选择页
│   ├── index.tsx
│   └── index.less
├── QuestionnaireSelect/   # 问卷选择页
│   ├── index.tsx
│   └── index.less
├── EvaluationForm/        # 问卷填写页
│   ├── index.tsx
│   └── index.less
└── README.md              # 说明文档

src/models/
└── parent.ts              # 家长端数据模型

src/services/
└── parent.ts              # 家长端API服务
```

### 📊 数据流管理

使用 umi 的 model 机制管理状态：

```typescript
const {
  // 状态
  loading,
  currentStep,
  parentPhone,
  studentList,
  selectedStudent,
  questionnaireList,
  selectedQuestionnaire,
  teacherList,
  evaluationData,
  isAllTeachersRated,

  // 方法
  verifyPhone,
  selectStudent,
  selectQuestionnaire,
  updateSchoolRating,
  updateTeacherRating,
  submitEvaluation,
  goBack,
} = useModel('parent');
```

### 💾 本地缓存机制

- **缓存键名**：`parent_evaluation_{phone}_{studentId}_{suffix}`
- **缓存内容**：评价数据（学校评分、教师评分、评价文本）
- **自动保存**：每次评分变更时自动保存
- **数据恢复**：页面刷新或重新进入时自动恢复
- **缓存清理**：提交成功后自动清除缓存

### 🔄 流程控制

```typescript
// 四级页面流程
'phone' → 'student' → 'questionnaire' → 'evaluation'

// 状态转换
verifyPhone() → currentStep = 'student'
selectStudent() → currentStep = 'questionnaire' (多问卷) 或 'evaluation' (单问卷)
selectQuestionnaire() → currentStep = 'evaluation'
submitEvaluation() → currentStep = 'phone' (重置)
```

## API 接口

### 家长端专用接口

```typescript
// 验证家长手机号
POST /api/parent/verify-phone
{
  phone: string
}

// 获取学生的所有问卷列表
GET /api/parent/student-questionnaires
{
  student_id: string,
  parent_phone: string
}

// 获取学生教师列表
GET /api/parent/student-teachers
{
  student_id: string,
  questionnaire_id?: number
}

// 获取问卷信息（单个）
GET /api/parent/questionnaire
{
  student_id: string,
  month?: string
}

// 提交评价
POST /api/parent/submit-evaluation
{
  questionnaire_id: number,
  student_id: string,
  parent_phone: string,
  evaluation_data: IEvaluationData
}

// 检查提交状态
GET /api/parent/check-submission
{
  student_id: string,
  questionnaire_id: number,
  parent_phone: string
}
```

## 使用说明

### 1. 访问家长端

**方式一：直接访问**
直接访问 `/parent` 路径，系统会自动显示手机号验证页面。

**方式二：自动认证访问**
访问 `/parent?phone=手机号` 路径，系统会自动尝试验证指定的手机号。

示例：
```
http://localhost:8081/parent?phone=13186034256
```

### 2. 手机号验证

**手动验证：**
- 输入在学校登记的家长手机号
- 系统验证手机号格式和关联信息
- 验证成功后自动跳转到学生选择页

**自动验证：**
- URL中包含phone参数时自动触发验证
- 验证成功后直接跳转到学生选择页
- 验证失败时显示错误信息，可手动重新输入

### 3. 选择学生

- 查看关联的学生列表
- 点击要评价的学生卡片
- 系统获取该学生的所有可填写问卷

### 4. 选择问卷

- 查看该学生的所有问卷列表
- 问卷按班级分组显示
- 查看问卷状态和有效期
- 点击可填写的问卷进入评价页面

### 5. 填写问卷

- 首先为学校整体表现评分
- 依次为每位教师评分
- 可选择性添加文字评价
- 完成所有评分后提交

### 6. 数据缓存

- 填写过程中数据自动保存到本地
- 页面刷新不会丢失已填写内容
- 提交成功后自动清除缓存

## 注意事项

1. **手机号要求**：必须使用在学校登记的家长手机号
2. **评价限制**：每个学生每月只能评价一次
3. **网络要求**：需要稳定的网络连接进行数据同步
4. **浏览器支持**：建议使用现代浏览器（Chrome、Safari、Edge 等）
5. **数据安全**：本地缓存仅保存在当前设备，不会上传到服务器

## 移动端优化

- 响应式布局适配各种屏幕尺寸
- 触摸友好的交互设计
- 优化的字体大小和间距
- 简化的操作流程
