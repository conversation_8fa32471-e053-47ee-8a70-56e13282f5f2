import { useModel } from '@umijs/max';
import { getQueryObj, removeQuery } from '@/utils/env';
import { message } from 'antd';
import React, { useEffect, useState } from 'react';
import EvaluationForm from './EvaluationForm';
import PhoneVerify from './PhoneVerify';
import QuestionnaireSelect from './QuestionnaireSelect';
import StudentSelect from './StudentSelect';

/**
 * 家长端主页面
 * @description 根据当前步骤渲染对应的页面组件，支持URL参数自动认证
 */
const ParentPage: React.FC = () => {
  const { currentStep, verifyPhone, loading } = useModel('parent');
  const [autoAuthAttempted, setAutoAuthAttempted] = useState(false);

  // 自动认证逻辑
  useEffect(() => {
    const handleAutoAuth = async () => {
      // 避免重复执行自动认证
      if (autoAuthAttempted) return;

      // 获取URL参数
      const queryParams = getQueryObj();
      const phoneFromUrl = queryParams.phone as string;

      // 如果URL中有手机号参数且当前在手机验证步骤
      if (phoneFromUrl && currentStep === 'phone') {
        setAutoAuthAttempted(true);

        // 验证手机号格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phoneFromUrl)) {
          message.error('URL中的手机号格式不正确，请手动输入正确的手机号');
          // 移除无效的手机号参数
          removeQuery('phone');
          return;
        }

        try {
          message.loading('正在自动验证手机号...', 0);
          const success = await verifyPhone(phoneFromUrl);
          message.destroy(); // 清除loading消息

          if (success) {
            message.success('手机号验证成功，正在跳转...');
            // 移除URL中的phone参数，避免重复认证
            removeQuery('phone');
          } else {
            message.error('自动验证失败，请手动输入手机号进行验证');
            // 保留phone参数，让用户可以看到预填充的手机号
          }
        } catch (error) {
          message.destroy(); // 清除loading消息
          message.error('自动验证过程中发生错误，请手动输入手机号');
          console.error('自动认证错误:', error);
        }
      }
    };

    handleAutoAuth();
  }, [currentStep, verifyPhone, autoAuthAttempted]);

  // 根据当前步骤渲染对应组件
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'phone':
        return <PhoneVerify />;
      case 'student':
        return <StudentSelect />;
      case 'questionnaire':
        return <QuestionnaireSelect />;
      case 'evaluation':
        return <EvaluationForm />;
      default:
        return <PhoneVerify />;
    }
  };

  return <div className="parent-page">{renderCurrentStep()}</div>;
};

export default ParentPage;
