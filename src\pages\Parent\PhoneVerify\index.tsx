import { ArrowRightOutlined, PhoneOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { getQueryObj } from '@/utils/env';
import { Alert, Button, Card, Form, Input, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

const { Title, Text } = Typography;

/**
 * 家长手机号验证页面
 * @description 支持从URL参数中预填充手机号
 */
const PhoneVerify: React.FC = () => {
  const { loading, verifyPhone } = useModel('parent');
  const [form] = Form.useForm();
  const [error, setError] = useState('');

  // 初始化时从URL参数中获取手机号并预填充
  useEffect(() => {
    const queryParams = getQueryObj();
    const phoneFromUrl = queryParams.phone as string;

    if (phoneFromUrl) {
      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (phoneRegex.test(phoneFromUrl)) {
        // 预填充手机号到表单
        form.setFieldsValue({ phone: phoneFromUrl });
      }
    }
  }, [form]);

  // 手机号验证规则
  const phoneRules = [
    { required: true, message: '请输入手机号' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式' },
  ];

  // 提交验证
  const handleSubmit = async (values: { phone: string }) => {
    setError('');
    const success = await verifyPhone(values.phone);
    if (!success) {
      setError('手机号验证失败，请检查是否已关联学生信息');
    }
  };

  return (
    <div className="phone-verify-container">
      <div className="phone-verify-content">
        <Card className="verify-card">
          <div className="card-header">
            <PhoneOutlined className="header-icon" />
            <Title level={3} className="header-title">
              家长身份验证
            </Title>
            <Text type="secondary" className="header-subtitle">
              请输入您的手机号进行身份验证
            </Text>
          </div>

          <div className="verify-form">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              size="large"
            >
              <Form.Item name="phone" label="手机号" rules={phoneRules}>
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="请输入您的手机号"
                  maxLength={11}
                />
              </Form.Item>

              {error && (
                <Form.Item>
                  <Alert
                    message={error}
                    type="error"
                    showIcon
                    closable
                    onClose={() => setError('')}
                  />
                </Form.Item>
              )}

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  icon={<ArrowRightOutlined />}
                  className="verify-button"
                >
                  验证并继续
                </Button>
              </Form.Item>
            </Form>
          </div>

          <div className="verify-tips">
            <Alert
              message="温馨提示"
              description={
                <div>
                  <p>• 请使用在学校登记的家长手机号进行验证</p>
                  <p>• 验证成功后可选择要评价的学生</p>
                  <p>• 每个学生每月只能评价一次</p>
                </div>
              }
              type="info"
              showIcon
            />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PhoneVerify;
