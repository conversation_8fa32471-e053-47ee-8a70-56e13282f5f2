import { defineConfig } from '@umijs/max';
import routes from './routes';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: '教师考核问卷系统',
  },
  define: {
    localStorage_prefix: 'teacherEvaluation_question_',
  },
  routes,
  npmClient: 'pnpm',
  proxy: {
    '/teacherEva_question_api': {
      target: 'http://127.0.0.1:3141',
      // target: 'http://150.158.55.60:3141',
      changeOrigin: true,
      pathRewrite: { '^/teacherEva_question_api': '' },
    },
  },
  favicons: ['/logo.png'],
});
