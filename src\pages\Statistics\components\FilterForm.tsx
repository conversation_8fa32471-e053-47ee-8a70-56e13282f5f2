import { getQuestionnaireList } from '@/services/questionnaire';
import type {
  IStatisticsQuery,
  IStatisticsTaskStatusResponse,
} from '@/types/statistics';
import { handleApiResponse } from '@/utils/errorHandler';
import {
  DownloadOutlined,
  ReloadOutlined,
  SearchOutlined,
  StopOutlined,
  ThunderboltOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Form,
  Row,
  Select,
  Space,
  Tooltip,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const { Option } = Select;

interface FilterFormProps {
  loading?: boolean;
  taskLoading?: boolean;
  isPolling?: boolean;
  statisticsTaskStatus?: IStatisticsTaskStatusResponse | null;
  onFilter: (values: IStatisticsQuery) => void;
  onReset: () => void;
  onTriggerStatistics?: (questionnaireId: number) => void;
  onRefreshStatus?: (questionnaireId: number) => void;
  onStopPolling?: () => void;
  onExport?: () => void;
}

/**
 * 数据筛选表单组件
 */
const FilterForm: React.FC<FilterFormProps> = ({
  loading = false,
  taskLoading = false,
  isPolling = false,
  statisticsTaskStatus,
  onFilter,
  onReset,
  onTriggerStatistics,
  onStopPolling,
  onExport,
}) => {
  const [form] = Form.useForm();
  const [questionnaireList, setQuestionnaireList] = useState<any[]>([]);
  const [questionnaireLoading, setQuestionnaireLoading] = useState(false);

  // 处理筛选
  const handleFilter = (values: any) => {
    const currentQuestionnaire = questionnaireList.find(
      (q) => q.id === values.questionnaire_id,
    );
    const filterParams: IStatisticsQuery = {
      questionnaire_id: values.questionnaire_id,
      month: currentQuestionnaire?.month,
      sso_school_code: currentQuestionnaire?.sso_school_code,
    };
    onFilter(filterParams);
  };

  // 根据年份获取问卷列表
  const fetchQuestionnaireListByYear = async (year?: string) => {
    if (!year) {
      setQuestionnaireList([]);
      return;
    }

    setQuestionnaireLoading(true);
    try {
      const response = await getQuestionnaireList({
        status: 'published', // 只获取已发布的问卷
        year, // 根据年份筛选
        page: 1,
        limit: 100,
      });
      const result = handleApiResponse(response);

      if (result.success) {
        const questionnaires = result.data?.list || [];
        setQuestionnaireList(questionnaires);

        // 如果有问卷，自动选择第一个并触发数据获取；如果没有，清空选择
        if (questionnaires.length > 0) {
          const firstQuestionnaire = questionnaires[0];
          form.setFieldsValue({
            questionnaire_id: firstQuestionnaire.id,
          });

          // 自动触发数据获取
          const filterParams: IStatisticsQuery = {
            questionnaire_id: firstQuestionnaire.id,
            month: firstQuestionnaire.month,
            sso_school_code: firstQuestionnaire.sso_school_code,
          };
          onFilter(filterParams);
        } else {
          form.setFieldsValue({
            questionnaire_id: undefined,
          });
          message.info(`${year} 年暂无可用问卷`);
        }
      } else {
        setQuestionnaireList([]);
        form.setFieldsValue({
          questionnaire_id: undefined,
        });
        message.error('获取问卷列表失败');
      }
    } catch (error) {
      setQuestionnaireList([]);
      form.setFieldsValue({
        questionnaire_id: undefined,
      });
      message.error('获取问卷列表失败');
    } finally {
      setQuestionnaireLoading(false);
    }
  };

  // 处理年份变化
  const handleYearChange = (year: string) => {
    // 年份变化时，重新获取该年份的问卷列表
    fetchQuestionnaireListByYear(year);
  };

  // 处理触发统计
  const handleTriggerStatistics = () => {
    const questionnaireId = form.getFieldValue('questionnaire_id');
    const year = form.getFieldValue('year');

    if (!year) {
      message.warning('请先选择年份');
      return;
    }

    if (!questionnaireId) {
      message.warning('当前年份暂无可用问卷，无法触发统计分析');
      return;
    }

    if (onTriggerStatistics) {
      onTriggerStatistics(questionnaireId);
    } else {
      console.warn('onTriggerStatistics 回调函数未定义');
    }
  };

  // 获取状态显示文本
  const getStatusText = () => {
    if (!statisticsTaskStatus) return '';

    let statusText = '';
    switch (statisticsTaskStatus.status) {
      case 'pending':
        statusText = '等待计算';
        break;
      case 'calculating':
        statusText = '计算中...';
        break;
      case 'completed':
        statusText = `计算完成 (${
          statisticsTaskStatus.last_calculated_at
            ? dayjs(statisticsTaskStatus.last_calculated_at).format(
                'MM-DD HH:mm',
              )
            : ''
        })`;
        break;
      case 'failed':
        statusText = '计算失败';
        break;
      default:
        statusText = '';
    }

    // 如果正在轮询，添加轮询标识
    if (
      isPolling &&
      (statisticsTaskStatus.status === 'calculating' ||
        statisticsTaskStatus.status === 'pending')
    ) {
      statusText += ' (自动刷新中)';
    }

    return statusText;
  };

  // 处理停止轮询
  const handleStopPolling = () => {
    if (onStopPolling) {
      onStopPolling();
    }
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  // 生成年份选项（最近12个月）
  const getYearOptions = () => {
    const years = [];
    const current = dayjs();

    for (let i = 0; i < 12; i++) {
      const year = current.subtract(i, 'year');
      years.push({
        value: year.format('YYYY'),
        label: year.format('YYYY年'),
      });
    }

    return years;
  };

  // 初始化数据
  useEffect(() => {
    const currentYear = dayjs().format('YYYY');

    // 设置默认年份
    form.setFieldsValue({
      year: currentYear,
    });

    // 根据默认年份获取问卷列表
    fetchQuestionnaireListByYear(currentYear);
  }, [form]);

  return (
    <Card
      className="filter-form-card"
      style={{
        marginBottom: 16,
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        border: 'none',
        color: 'white',
      }}
      styles={{ body: { padding: '20px 24px' } }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <div
          style={{
            fontSize: '18px',
            fontWeight: 600,
            color: 'white',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <SearchOutlined style={{ marginRight: 8, fontSize: '20px' }} />
          数据筛选
        </div>
        {statisticsTaskStatus && (
          <div
            style={{
              background: 'rgba(255,255,255,0.2)',
              padding: '4px 12px',
              borderRadius: '12px',
              fontSize: '12px',
              color: 'white',
              fontWeight: 500,
            }}
          >
            状态：{getStatusText()}
          </div>
        )}
      </div>

      <Form
        form={form}
        layout="inline"
        onFinish={handleFilter}
        style={{ width: '100%' }}
      >
        <Row gutter={16} style={{ width: '100%' }}>
          <Col xl={6} span={12}>
            <Form.Item
              name="year"
              label={
                <span style={{ color: 'white', fontWeight: 500 }}>年份</span>
              }
            >
              <Select
                placeholder="请选择年份"
                allowClear
                style={{ width: '100%' }}
                size="large"
                onChange={handleYearChange}
              >
                {getYearOptions().map((year) => (
                  <Option key={year.value} value={year.value}>
                    {year.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xl={6} span={12}>
            <Form.Item
              name="questionnaire_id"
              label={
                <span style={{ color: 'white', fontWeight: 500 }}>问卷</span>
              }
            >
              <Select
                placeholder={
                  !form.getFieldValue('year')
                    ? '请先选择年份'
                    : questionnaireList.length === 0
                    ? '该年份暂无问卷'
                    : '请选择问卷'
                }
                style={{ width: '100%' }}
                size="large"
                loading={questionnaireLoading}
                disabled={
                  !form.getFieldValue('year') || questionnaireList.length === 0
                }
                notFoundContent={
                  questionnaireLoading
                    ? '加载中...'
                    : !form.getFieldValue('year')
                    ? '请先选择年份'
                    : '该年份暂无问卷'
                }
              >
                {questionnaireList.map((questionnaire) => (
                  <Option key={questionnaire.id} value={questionnaire.id}>
                    {questionnaire.title}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col xl={12} span={24}>
            <Form.Item>
              <Space wrap>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                  size="large"
                  style={{
                    background: 'rgba(255,255,255,0.2)',
                    borderColor: 'rgba(255,255,255,0.3)',
                    color: 'white',
                    fontWeight: 500,
                  }}
                >
                  查询
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleReset}
                  size="large"
                  style={{
                    background: 'transparent',
                    borderColor: 'rgba(255,255,255,0.3)',
                    color: 'white',
                  }}
                >
                  重置
                </Button>
                <Tooltip
                  title={
                    !form.getFieldValue('year')
                      ? '请先选择年份'
                      : !form.getFieldValue('questionnaire_id')
                      ? '该年份暂无可用问卷，无法触发统计'
                      : '手动触发统计计算，系统将异步执行统计任务'
                  }
                >
                  <Button
                    type="dashed"
                    icon={<ThunderboltOutlined />}
                    loading={taskLoading}
                    onClick={handleTriggerStatistics}
                    size="large"
                    disabled={
                      !form.getFieldValue('year') ||
                      !form.getFieldValue('questionnaire_id')
                    }
                    style={{
                      background: 'rgba(255,255,255,0.1)',
                      borderColor: 'rgba(255,255,255,0.3)',
                      color: 'white',
                      opacity:
                        !form.getFieldValue('year') ||
                        !form.getFieldValue('questionnaire_id')
                          ? 0.5
                          : 1,
                    }}
                  >
                    触发统计
                  </Button>
                </Tooltip>
                <Tooltip title="导出统计数据">
                  <Button
                    type="primary"
                    icon={<DownloadOutlined />}
                    onClick={onExport}
                    size="large"
                    style={{
                      background: 'rgba(255,255,255,0.2)',
                      borderColor: 'rgba(255,255,255,0.3)',
                      color: 'white',
                      fontWeight: 500,
                    }}
                  >
                    导出
                  </Button>
                </Tooltip>
                {isPolling && (
                  <Tooltip title="停止自动刷新统计状态">
                    <Button
                      type="default"
                      icon={<StopOutlined />}
                      onClick={handleStopPolling}
                      size="small"
                      style={{
                        background: 'rgba(255,255,255,0.1)',
                        borderColor: 'rgba(255,255,255,0.3)',
                        color: 'white',
                      }}
                    >
                      停止轮询
                    </Button>
                  </Tooltip>
                )}
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default FilterForm;
